// API client for communicating with the backend server

export class ApiClient {
  constructor() {
    // TODO: Replace with actual backend URL
    this.baseUrl = 'http://localhost:3001'; // NestJS backend URL
    this.webAppUrl = 'http://localhost:3000'; // Next.js frontend URL
  }

  async makeRequest(endpoint, options = {}) {
    try {
      const token = await this.getAuthToken();
      console.log('🔑 [API Client] Token available:', token ? 'YES' : 'NO');
      console.log('🔑 [API Client] Token length:', token ? token.length : 0);

      const defaultOptions = {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      };

      const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
          ...defaultOptions.headers,
          ...options.headers
        }
      };

      console.log('🌐 [API Client] Making request to:', `${this.baseUrl}${endpoint}`);
      console.log('🌐 [API Client] Request headers:', requestOptions.headers);

      const response = await fetch(`${this.baseUrl}${endpoint}`, requestOptions);

      console.log('📡 [API Client] Response status:', response.status);

      if (response.status === 401) {
        console.log('🚫 [API Client] 401 Unauthorized - clearing token');
        // Token expired or invalid
        await this.clearAuthToken();
        throw new Error('Authentication required');
      }

      const data = await response.json();

      if (!response.ok) {
        console.error('❌ [API Client] Request failed:', data);
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      console.log('✅ [API Client] Request successful');
      return { success: true, data };
    } catch (error) {
      console.error('❌ [API Client] API request failed:', error);
      return { success: false, error: error.message };
    }
  }

  async getAuthToken() {
    const result = await chrome.storage.local.get(['authToken']);
    return result.authToken;
  }

  async setAuthToken(token) {
    await chrome.storage.local.set({
      authToken: token,
      isAuthenticated: true
    });
  }

  async clearAuthToken() {
    await chrome.storage.local.remove(['authToken']);
    await chrome.storage.local.set({ isAuthenticated: false });
  }

  async checkAuthStatus() {
    // First, try to sync with web app authentication
    const webAppAuth = await this.checkWebAppAuth();
    if (webAppAuth.isAuthenticated) {
      return webAppAuth;
    }

    // Fallback to extension's own token
    const token = await this.getAuthToken();

    if (!token) {
      return { isAuthenticated: false };
    }

    // Verify token with backend
    const result = await this.makeRequest('/auth/verify');

    if (result.success) {
      return {
        isAuthenticated: true,
        user: result.data
      };
    } else {
      await this.clearAuthToken();
      return { isAuthenticated: false };
    }
  }

  async checkWebAppAuth() {
    try {
      console.log('🔍 [API Client] Checking for web app authentication...');

      // Check if user has the web app open in any tab
      const tabs = await chrome.tabs.query({ url: `${this.webAppUrl}/*` });
      console.log(`🔍 [API Client] Found ${tabs.length} web app tabs:`, tabs.map(t => ({ id: t.id, url: t.url })));

      for (const tab of tabs) {
        try {
          console.log(`📡 [API Client] Sending auth status request to tab ${tab.id}...`);

          // Try to get auth status from web app tab
          const response = await chrome.tabs.sendMessage(tab.id, {
            type: 'GET_WEB_APP_AUTH_STATUS'
          });

          console.log(`📨 [API Client] Response from tab ${tab.id}:`, response);

          if (response && response.isAuthenticated) {
            console.log('✅ [API Client] Found authenticated session in web app!');
            console.log('🔑 [API Client] Access token in response:', response.accessToken ? 'YES' : 'NO');

            // Sync the token to extension storage
            if (response.accessToken) {
              await this.setAuthToken(response.accessToken);
              console.log('🔄 [API Client] Synced access token to extension storage');
            } else {
              console.log('⚠️ [API Client] No access token in response - authentication may fail');
            }

            return {
              isAuthenticated: true,
              user: response.user,
              source: 'webapp'
            };
          } else {
            console.log(`❌ [API Client] Tab ${tab.id} not authenticated or no response`);
          }
        } catch (error) {
          // Tab might not have content script injected, continue to next tab
          console.log(`❌ [API Client] Could not get auth status from tab ${tab.id}:`, error.message);
        }
      }

      console.log('❌ [API Client] No authenticated web app sessions found');
      return { isAuthenticated: false };
    } catch (error) {
      console.error('❌ [API Client] Error checking web app auth:', error);
      return { isAuthenticated: false };
    }
  }

  async getLoginUrl() {
    // Return the web application login URL
    return `${this.webAppUrl}/auth/signin?source=extension`;
  }

  async logout() {
    const result = await this.makeRequest('/auth/logout', {
      method: 'POST'
    });

    await this.clearAuthToken();
    return result;
  }

  async createCrawledProduct(productData) {
    return this.makeRequest('/crawled-products', {
      method: 'POST',
      body: JSON.stringify(productData)
    });
  }

  async getCrawledProducts(query = {}) {
    const queryString = new URLSearchParams(query).toString();
    const endpoint = `/crawled-products${queryString ? `?${queryString}` : ''}`;

    return this.makeRequest(endpoint);
  }

  async getCrawlSchedules() {
    return this.makeRequest('/crawl-schedules');
  }

  async createCrawlSchedule(scheduleData) {
    return this.makeRequest('/crawl-schedules', {
      method: 'POST',
      body: JSON.stringify(scheduleData)
    });
  }

  async updateCrawlSchedule(scheduleId, scheduleData) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}`, {
      method: 'PUT',
      body: JSON.stringify(scheduleData)
    });
  }

  async deleteCrawlSchedule(scheduleId) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}`, {
      method: 'DELETE'
    });
  }

  async toggleCrawlSchedule(scheduleId) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}/toggle`, {
      method: 'POST'
    });
  }

  // Method to handle authentication from web app
  async handleAuthCallback(token) {
    if (token) {
      await this.setAuthToken(token);
      return { success: true };
    } else {
      return { success: false, error: 'No token provided' };
    }
  }

  // Method to get user profile
  async getUserProfile() {
    return this.makeRequest('/auth/profile');
  }

  // Method to refresh auth token
  async refreshToken() {
    const result = await this.makeRequest('/auth/refresh', {
      method: 'POST'
    });

    if (result.success && result.data.accessToken) {
      await this.setAuthToken(result.data.accessToken);
    }

    return result;
  }
}
