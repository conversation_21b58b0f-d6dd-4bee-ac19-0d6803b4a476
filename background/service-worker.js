// Background service worker for the Product Crawler extension
// Handles extension lifecycle, authentication, and automated crawling

import { ApiClient } from './api-client.js';
import { CrawlScheduler } from './scheduler.js';

class ServiceWorker {
  constructor() {
    this.apiClient = new ApiClient();
    this.scheduler = new CrawlScheduler(this.apiClient);
    this.init();
  }

  init() {
    // Listen for extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      console.log('Product Crawler extension installed:', details);
      this.handleInstallation(details);
    });

    // Listen for messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Listen for alarm events (for scheduled crawling)
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });

    // Listen for tab updates (for detecting marketplace pages)
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });
  }

  async handleInstallation(details) {
    if (details.reason === 'install') {
      // First time installation
      console.log('First time installation - setting up defaults');
      await this.setupDefaults();
    } else if (details.reason === 'update') {
      // Extension update
      console.log('Extension updated from version:', details.previousVersion);
    }
  }

  async setupDefaults() {
    // Set default configuration
    await chrome.storage.local.set({
      isAuthenticated: false,
      settings: {
        autoDetectProducts: true,
        showNotifications: true,
        maxCrawlDelay: 5000,
        minCrawlDelay: 1000
      }
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'EXTRACT_PRODUCT':
          await this.handleProductExtraction(message.data, sendResponse);
          break;

        case 'GET_AUTH_STATUS':
          await this.handleGetAuthStatus(sendResponse);
          break;

        case 'LOGIN':
          await this.handleLogin(sendResponse);
          break;

        case 'LOGOUT':
          await this.handleLogout(sendResponse);
          break;

        case 'GET_CRAWL_SCHEDULES':
          await this.handleGetCrawlSchedules(sendResponse);
          break;

        case 'CREATE_CRAWL_SCHEDULE':
          await this.handleCreateCrawlSchedule(message.data, sendResponse);
          break;

        case 'SYNC_WEB_APP_AUTH':
          await this.handleSyncWebAppAuth(message.data, sendResponse);
          break;

        default:
          console.warn('Unknown message type:', message.type);
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleProductExtraction(productData, sendResponse) {
    try {
      // Check if user is authenticated
      const authStatus = await this.apiClient.checkAuthStatus();
      if (!authStatus.isAuthenticated) {
        sendResponse({
          success: false,
          error: 'User not authenticated',
          requiresAuth: true
        });
        return;
      }

      // Send product data to backend
      const result = await this.apiClient.createCrawledProduct(productData);

      // Show success notification
      if (result.success) {
        this.showNotification('Product Extracted',
          `Successfully extracted: ${productData.title}`);
      }

      sendResponse(result);
    } catch (error) {
      console.error('Error extracting product:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleGetAuthStatus(sendResponse) {
    try {
      console.log('Checking authentication status...');
      const authStatus = await this.apiClient.checkAuthStatus();
      console.log('Auth status result:', authStatus);
      sendResponse({ success: true, data: authStatus });
    } catch (error) {
      console.error('Error checking auth status:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleLogin(sendResponse) {
    try {
      // Open web application login page
      const loginUrl = await this.apiClient.getLoginUrl();
      const tab = await chrome.tabs.create({ url: loginUrl });

      // Listen for authentication completion
      const authResult = await this.waitForAuthentication(tab.id);
      sendResponse(authResult);
    } catch (error) {
      console.error('Error during login:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleLogout(sendResponse) {
    try {
      await this.apiClient.logout();
      await chrome.storage.local.set({ isAuthenticated: false });
      sendResponse({ success: true });
    } catch (error) {
      console.error('Error during logout:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleGetCrawlSchedules(sendResponse) {
    try {
      const schedules = await this.apiClient.getCrawlSchedules();
      sendResponse({ success: true, data: schedules });
    } catch (error) {
      console.error('Error getting crawl schedules:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleCreateCrawlSchedule(scheduleData, sendResponse) {
    try {
      const result = await this.apiClient.createCrawlSchedule(scheduleData);

      if (result.success) {
        // Set up alarm for this schedule
        await this.scheduler.setupScheduleAlarm(result.data);
      }

      sendResponse(result);
    } catch (error) {
      console.error('Error creating crawl schedule:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleSyncWebAppAuth(authData, sendResponse) {
    try {
      console.log('Syncing web app authentication:', authData);

      if (authData && authData.accessToken) {
        await this.apiClient.setAuthToken(authData.accessToken);
        console.log('Successfully synced authentication token from web app');
        sendResponse({ success: true, message: 'Authentication synced' });
      } else {
        console.log('No authentication data to sync');
        sendResponse({ success: false, error: 'No authentication data provided' });
      }
    } catch (error) {
      console.error('Error syncing web app auth:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleAlarm(alarm) {
    console.log('Alarm triggered:', alarm.name);

    if (alarm.name.startsWith('crawl_schedule_')) {
      const scheduleId = alarm.name.replace('crawl_schedule_', '');
      await this.scheduler.executeCrawlSchedule(scheduleId);
    }
  }

  async handleTabUpdate(tabId, changeInfo, tab) {
    // Check if tab finished loading and is on a supported marketplace
    if (changeInfo.status === 'complete' && tab.url) {
      const isMarketplace = this.isMarketplacePage(tab.url);

      if (isMarketplace) {
        // Inject content script if needed and update popup state
        await this.updatePopupForMarketplace(tabId, tab.url);
      }
    }
  }

  isMarketplacePage(url) {
    const marketplaces = [
      'etsy.com/listing/',
      'ebay.com/itm/',
      'amazon.com/dp/',
      'amazon.com/gp/product/'
    ];

    return marketplaces.some(marketplace => url.includes(marketplace));
  }

  async updatePopupForMarketplace(tabId, url) {
    // Store current tab info for popup access
    await chrome.storage.local.set({
      currentTab: {
        id: tabId,
        url: url,
        isMarketplace: true,
        marketplace: this.getMarketplaceFromUrl(url)
      }
    });
  }

  getMarketplaceFromUrl(url) {
    if (url.includes('etsy.com')) return 'etsy';
    if (url.includes('ebay.com')) return 'ebay';
    if (url.includes('amazon.com')) return 'amazon';
    return 'unknown';
  }

  async waitForAuthentication(tabId) {
    return new Promise((resolve) => {
      const checkAuth = async () => {
        try {
          const authStatus = await this.apiClient.checkAuthStatus();
          if (authStatus.isAuthenticated) {
            chrome.tabs.remove(tabId);
            resolve({ success: true, data: authStatus });
          } else {
            setTimeout(checkAuth, 1000);
          }
        } catch (error) {
          resolve({ success: false, error: error.message });
        }
      };

      setTimeout(checkAuth, 1000);
    });
  }

  showNotification(title, message) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: title,
      message: message
    });
  }
}

// Initialize the service worker
new ServiceWorker();
